// Shared color options for flashcards
export interface ColorOption {
  value: string;
  label: string;
  bgClass: string;
  borderClass: string;
  iconClass: string;
  buttonClass: string;
  selectClass?: string; // Optional class for select components
}

export const colorOptions: ColorOption[] = [
  {
    value: 'blue',
    label: 'Blue',
    bgClass: 'bg-blue-50 dark:bg-blue-950/30',
    borderClass: 'border-blue-300 dark:border-blue-700',
    iconClass: 'text-blue-600 dark:text-blue-400',
    buttonClass: 'bg-blue-500 hover:bg-blue-600',
    selectClass: 'bg-blue-500',
  },
  {
    value: 'green',
    label: 'Green',
    bgClass: 'bg-green-50 dark:bg-green-950/30',
    borderClass: 'border-green-300 dark:border-green-700',
    iconClass: 'text-green-600 dark:text-green-400',
    buttonClass: 'bg-green-500 hover:bg-green-600',
    selectClass: 'bg-green-500',
  },
  {
    value: 'purple',
    label: 'Purple',
    bgClass: 'bg-purple-50 dark:bg-purple-950/30',
    borderClass: 'border-purple-300 dark:border-purple-700',
    iconClass: 'text-purple-600 dark:text-purple-400',
    buttonClass: 'bg-purple-500 hover:bg-purple-600',
    selectClass: 'bg-purple-500',
  },
  {
    value: 'red',
    label: 'Red',
    bgClass: 'bg-red-50 dark:bg-red-950/30',
    borderClass: 'border-red-300 dark:border-red-700',
    iconClass: 'text-red-600 dark:text-red-400',
    buttonClass: 'bg-red-500 hover:bg-red-600',
    selectClass: 'bg-red-500',
  },
  {
    value: 'orange',
    label: 'Orange',
    bgClass: 'bg-orange-50 dark:bg-orange-950/30',
    borderClass: 'border-orange-300 dark:border-orange-700',
    iconClass: 'text-orange-600 dark:text-orange-400',
    buttonClass: 'bg-orange-500 hover:bg-orange-600',
    selectClass: 'bg-orange-500',
  },
  {
    value: 'pink',
    label: 'Pink',
    bgClass: 'bg-pink-50 dark:bg-pink-950/30',
    borderClass: 'border-pink-300 dark:border-pink-700',
    iconClass: 'text-pink-600 dark:text-pink-400',
    buttonClass: 'bg-pink-500 hover:bg-pink-600',
    selectClass: 'bg-pink-500',
  },
];
