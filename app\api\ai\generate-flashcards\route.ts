import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

const MAX_CONTENT_LENGTH = 512;

const FlashcardDataSchema = z.object({
  columns: z.array(z.string()),
  data: z.array(z.record(z.string())),
  hasHtml: z.boolean(),
});

const FlashcardWithDescriptionSchema = z.object({
  description: z.string(),
  columns: z.array(z.string()),
  data: z.array(z.record(z.string())),
  hasHtml: z.boolean(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { content, description } = body;

    if (typeof content !== 'string' || content.trim().length === 0) {
      return NextResponse.json({ error: 'Content must be a non-empty string' }, { status: 400 });
    }

    if (content.length > MAX_CONTENT_LENGTH) {
      return NextResponse.json(
        { error: `Content must be less than ${MAX_CONTENT_LENGTH} characters` },
        { status: 400 },
      );
    }

    let finalDescription: string;
    let flashcardData: z.infer<typeof FlashcardDataSchema>;

    if (description && typeof description === 'string' && description.trim().length > 0) {
      finalDescription = description.trim();
      const { object } = await generateObject({
        model: 'openai/gpt-5-mini',
        schema: FlashcardDataSchema,
        system: `You are an expert educational content creator specializing in creating flashcards. Your task is to generate educational flashcard content that is:
- Accurate and factual
- Well-structured with clear columns
- Column names must be consistent
- Balanced in terms of information density per card
`,
        prompt: `Generate data for the following topic: ${content}`,
      });
      flashcardData = object;
    } else {
      const { object } = await generateObject({
        model: 'openai/gpt-5-mini',
        schema: FlashcardWithDescriptionSchema,
        system: `You are an expert educational content creator specializing in creating flashcards. Your task is to generate educational flashcard content that is:
- Accurate and factual
- Well-structured with clear columns
- Column names must be consistent
- Balanced in terms of information density per card
- Includes a concise, informative description of the deck
`,
        prompt: `Generate data and a description for the following topic: ${content}`,
      });
      finalDescription = object.description;
      flashcardData = {
        columns: object.columns,
        data: object.data,
        hasHtml: object.hasHtml,
      };
    }

    console.log(
      'AI Response:',
      JSON.stringify({ description: finalDescription, ...flashcardData }, null, 2),
    ); // Temporary logging for debugging

    return NextResponse.json({ description: finalDescription, ...flashcardData });
  } catch (error) {
    console.error('Error generating flashcards:', error);
    return NextResponse.json({ error: 'Failed to generate flashcards' }, { status: 500 });
  }
}
