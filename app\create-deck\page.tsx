'use client';

import { useUser, withPageAuthRequired } from '@auth0/nextjs-auth0';
import { ArrowLeft, Save, CheckCircle, AlertCircle, BookOpen } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

import { ColumnSelector } from '@/components/column-selector';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Textarea } from '@/components/ui/textarea';

export interface FlashcardData {
  [key: string]: string;
}

export interface DeckCreationState {
  step: 'info' | 'configure';
  data: FlashcardData[];
  columns: string[];
  frontColumns: string[];
  backColumns: string[];
  name: string;
  description: string;
  topic: string;
  hasHtml: boolean;
  isGenerating: boolean;
  isSaving: boolean;
  error: string | null;
  success: boolean;
}

function CreateDeckContent() {
  const { user } = useUser();
  const router = useRouter();
  const [state, setState] = useState<DeckCreationState>({
    step: 'info',
    data: [],
    columns: [],
    frontColumns: [],
    backColumns: [],
    name: '',
    description: '',
    topic: '',
    hasHtml: false,
    isGenerating: false,
    isSaving: false,
    error: null,
    success: false,
  });
  const [isTableVisible, setIsTableVisible] = useState(false);

  const handleGenerateFlashcards = async () => {
    if (!state.topic.trim()) {
      setState((prev) => ({
        ...prev,
        error: 'Please enter study material to generate flashcards',
      }));
      return;
    }

    // Check for harmful content before generating flashcards
    setState((prev) => ({
      ...prev,
      isGenerating: true,
      error: null,
    }));

    try {
      const moderationResponse = await fetch('/api/moderation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: state.name.trim(),
          description: state.description.trim(),
          topic: state.topic.trim(),
        }),
      });

      const moderationData = await moderationResponse.json();

      if (moderationData.isHarmful) {
        setState((prev) => ({
          ...prev,
          error: `Inappropriate or harmful content detected. Try again.`,
          isGenerating: false,
        }));
        return;
      }
    } catch (error) {
      console.error('Error during moderation check:', error);
      // Continue with flashcard generation if moderation fails
    }

    // Continue with flashcard generation
    try {
      const response = await fetch('/api/ai/generate-flashcards', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: state.topic.trim(),
          description: state.description.trim(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate flashcards');
      }

      const data = await response.json();
      setState((prev) => ({
        ...prev,
        step: 'configure',
        data: data.data,
        columns: data.columns,
        hasHtml: data.hasHtml,
        description: data.description || prev.description, // Use AI-generated description if user didn't provide one
        frontColumns: data.columns.length > 0 ? [data.columns[0]] : [],
        backColumns: data.columns.length > 1 ? [data.columns[1]] : [],
        isGenerating: false,
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to generate flashcards',
        isGenerating: false,
      }));
    }
  };

  const handleColumnsChange = (frontColumns: string[], backColumns: string[]) => {
    setState((prev) => ({
      ...prev,
      frontColumns,
      backColumns,
    }));
  };

  const handleSaveDeck = async () => {
    if (!user?.sub || !state.name.trim()) {
      setState((prev) => ({
        ...prev,
        error: 'Please provide a deck name',
      }));
      return;
    }

    if (state.frontColumns.length === 0 || state.backColumns.length === 0) {
      setState((prev) => ({
        ...prev,
        error: 'Please select at least one column for front and back',
      }));
      return;
    }

    setState((prev) => ({
      ...prev,
      isSaving: true,
      error: null,
    }));

    try {
      const response = await fetch('/api/decks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: state.name.trim(),
          description: state.description.trim(),
          data: state.data,
          columns: state.columns,
          frontColumns: state.frontColumns,
          backColumns: state.backColumns,
          hasHtml: state.hasHtml,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save deck');
      }

      const data = await response.json();
      router.push(`/study?deck=${data.deck.id}`);
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to save deck',
        isSaving: false,
      }));
    }
  };

  const handleBackToInfo = () => {
    setState((prev) => ({
      ...prev,
      step: 'info',
      data: [],
      columns: [],
      frontColumns: [],
      backColumns: [],
      hasHtml: false,
      error: null,
    }));
  };

  return (
    <main className="bg-background">
      <div className="container mx-auto max-w-4xl px-4 py-8">
        <div className="mb-4 text-center">
          <h1
            className="text-foreground mb-2 text-4xl md:text-5xl font-bold text-balance"
            style={{ fontFamily: 'var(--font-londrina-sketch)' }}
          >
            Create a New Deck
          </h1>
          <p className="text-muted-foreground text-md md:text-lg text-pretty">
            Generate personalized flashcards
          </p>
        </div>

        {/* Deck Info Form - Hidden during configuration step */}
        {state.step !== 'configure' && (
          <Card className="mb-6 overflow-hidden rounded-xl border bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 border-slate-200 dark:border-slate-700 p-5 shadow-sm transition-all duration-300 hover:shadow-md">
            <CardHeader className="p-0">
              <CardTitle className="flex items-center gap-3 text-foreground">
                <div className="relative">
                  <div className="absolute inset-0 bg-indigo-500 rounded-full blur-md opacity-20 animate-pulse"></div>
                  <div className="h-10 w-10 rounded-full bg-gradient-to-br from-indigo-100 to-indigo-200 dark:from-indigo-900 dark:to-indigo-800 flex items-center justify-center relative z-10">
                    <BookOpen className="h-5 w-5 text-indigo-600 dark:text-indigo-300" />
                  </div>
                </div>
                Add basic information to your flashcard deck
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 p-0">
              <div className="space-y-2">
                <Label htmlFor="deck-name">Deck Name *</Label>
                <Input
                  id="deck-name"
                  placeholder="Enter deck name"
                  value={state.name}
                  onChange={(e) => setState((prev) => ({ ...prev, name: e.target.value }))}
                  disabled={state.isSaving || state.success || state.isGenerating}
                  className="bg-background"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="deck-description">Description</Label>
                <Input
                  id="deck-description"
                  placeholder="Optional description of your deck"
                  value={state.description}
                  onChange={(e) => setState((prev) => ({ ...prev, description: e.target.value }))}
                  disabled={state.isSaving || state.success || state.isGenerating}
                  className="bg-background"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="deck-topic">Topic *</Label>
                <Textarea
                  id="deck-topic"
                  placeholder="Explain the topic you want to study, also include any additional context or instructions"
                  value={state.topic}
                  onChange={(e) => setState((prev) => ({ ...prev, topic: e.target.value }))}
                  disabled={state.isSaving || state.success || state.isGenerating}
                  rows={6}
                  className="bg-background"
                />
                <p className="text-sm text-muted-foreground mt-2">
                  For example: "100 JLPT N5 vocabulary", "Japanese drinks with kanji"
                </p>
                <Button
                  onClick={handleGenerateFlashcards}
                  disabled={state.isGenerating || !state.topic.trim() || !state.name.trim()}
                  className="w-full mt-2"
                >
                  {state.isGenerating ? (
                    <>
                      <div className="border-accent h-4 w-4 animate-spin rounded-full border-b-2"></div>
                      Generating Flashcards...
                    </>
                  ) : (
                    'Generate Flashcards'
                  )}
                </Button>
                {state.isGenerating && (
                  <div className="mt-4 p-3">
                    <p className="text-sm text-center text-muted-foreground">
                      This may take a few minutes. You can safely come back later, but please do not
                      close this page.
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Generation Results Card - Shown after successful generation */}
        {state.step === 'configure' && !state.isGenerating && (
          <Card className="mb-6 overflow-hidden rounded-xl border bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 border-slate-200 dark:border-slate-700 p-5 shadow-sm transition-all duration-300 hover:shadow-md">
            <CardHeader className="p-0">
              <CardTitle className="flex items-center gap-3 text-foreground">
                <div className="relative">
                  <div className="absolute inset-0 bg-green-500 rounded-full blur-md opacity-20 animate-pulse"></div>
                  <div className="h-10 w-10 rounded-full bg-gradient-to-br from-green-100 to-green-200 dark:from-green-900 dark:to-green-800 flex items-center justify-center relative z-10">
                    <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-300" />
                  </div>
                </div>
                Flashcards Generated
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 p-0">
              <div className="space-y-2">
                <Label>Deck Name</Label>
                <div className="text-foreground rounded-md border p-3 font-medium">
                  {state.name}
                </div>
              </div>
              {state.description && (
                <div className="space-y-2">
                  <Label>Description</Label>
                  <div className="text-foreground rounded-md border p-3">{state.description}</div>
                </div>
              )}
              <div className="space-y-4">
                <Button
                  variant="outline"
                  onClick={() => setIsTableVisible(!isTableVisible)}
                  className="w-full"
                >
                  {isTableVisible ? 'Hide Generated Cards' : 'Show Generated Cards'}
                </Button>
                {isTableVisible && state.data.length > 0 && (
                  <div className="rounded-md border border-slate-200 dark:border-slate-700 overflow-hidden shadow-sm">
                    <Table>
                      <TableHeader className="bg-slate-50 dark:bg-slate-900">
                        <TableRow>
                          {state.columns.map((column) => (
                            <TableHead key={column} className="font-bold text-foreground">
                              {column}
                            </TableHead>
                          ))}
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {state.data.map((row, index) => (
                          <TableRow
                            key={index}
                            className="hover:bg-slate-50/50 dark:hover:bg-slate-900/10"
                          >
                            {state.columns.map((column) => (
                              <TableCell key={`${index}-${column}`} className="align-top">
                                {state.hasHtml ? (
                                  <div dangerouslySetInnerHTML={{ __html: row[column] || '' }} />
                                ) : (
                                  row[column] || ''
                                )}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </div>
              <div className="flex items-center gap-2 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/30 dark:to-emerald-900/30 p-3 rounded-lg border border-green-200 dark:border-green-800">
                <div className="bg-green-500 rounded-full h-2 w-2"></div>
                <p className="text-sm text-green-700 dark:text-green-300">
                  Generated <b>{state.data.length}</b> card
                  {state.data.length !== 1 ? 's' : ''}. Configure your deck below.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error Alert */}
        {state.error && (
          <div
            role="alert"
            aria-live="assertive"
            className="text-destructive p-4 border border-destructive rounded-lg bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-950/30 dark:to-pink-950/30 mb-6"
          >
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-red-500 dark:text-red-300" />
              <p className="font-medium text-red-700 dark:text-red-300">{state.error}</p>
            </div>
          </div>
        )}

        {/* Configure Step */}
        {state.step === 'configure' && !state.success && (
          <div className="space-y-6">
            <ColumnSelector
              columns={state.columns}
              frontColumns={state.frontColumns}
              backColumns={state.backColumns}
              data={state.data}
              hasHtml={state.hasHtml}
              disabled={state.isSaving}
              onColumnsChange={handleColumnsChange}
            />

            {/* Action Buttons */}
            <div className="flex justify-between gap-4">
              <Button
                variant="outline"
                onClick={handleBackToInfo}
                disabled={state.isSaving}
                className="flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Discard
              </Button>
              <Button
                onClick={handleSaveDeck}
                disabled={
                  state.isSaving ||
                  !state.name.trim() ||
                  state.frontColumns.length === 0 ||
                  state.backColumns.length === 0
                }
                className="flex items-center gap-2"
              >
                {state.isSaving ? (
                  <>
                    <div className="border-accent h-4 w-4 animate-spin rounded-full border-b-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4" />
                    Save Deck
                  </>
                )}
              </Button>
            </div>
          </div>
        )}
      </div>
    </main>
  );
}

// Protect the create deck page with authentication
export default withPageAuthRequired(CreateDeckContent, {
  returnTo: '/create-deck',
});
