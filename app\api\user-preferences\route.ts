import { NextRequest, NextResponse } from 'next/server';

import { auth0 } from '@/lib/auth0';
import pool from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const session = await auth0.getSession(request);
    if (!session?.user?.sub) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const userId = session.user.sub;

    try {
      // Get user preferences
      const result = await pool.query(
        'SELECT preferred_color, preferred_randomized FROM user_progress WHERE user_id = $1',
        [userId],
      );

      if (result.rows.length === 0) {
        // Return default preferences if no record exists
        return NextResponse.json({
          preferred_color: 'blue',
          preferred_randomized: false,
        });
      }

      return NextResponse.json({
        preferred_color: result.rows[0].preferred_color || 'blue',
        preferred_randomized: result.rows[0].preferred_randomized || false,
      });
    } catch (error: unknown) {
      // Handle case where preferred columns don't exist yet
      if (error instanceof Error && 'code' in error && error.code === '42703') {
        // Column doesn't exist, return defaults
        return NextResponse.json({
          preferred_color: 'blue',
          preferred_randomized: false,
        });
      }
      throw error;
    }
  } catch (error) {
    console.error('Error fetching user preferences:', error);
    return NextResponse.json({ error: 'Failed to fetch user preferences' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const session = await auth0.getSession(request);
    if (!session?.user?.sub) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const userId = session.user.sub;
    const body = await request.json();
    const { preferred_color, preferred_randomized } = body;

    // Validate inputs
    const validColors = ['blue', 'red', 'green', 'purple', 'orange', 'pink', 'yellow', 'gray'];
    if (preferred_color && !validColors.includes(preferred_color)) {
      return NextResponse.json({ error: 'Invalid color preference' }, { status: 400 });
    }
    if (preferred_randomized !== undefined && typeof preferred_randomized !== 'boolean') {
      return NextResponse.json({ error: 'Invalid randomized preference' }, { status: 400 });
    }

    try {
      // Build dynamic update query based on provided fields
      const updates = [];
      const values = [];
      let paramCount = 1;

      if (preferred_color !== undefined) {
        updates.push(`preferred_color = $${paramCount}`);
        values.push(preferred_color);
        paramCount++;
      }
      if (preferred_randomized !== undefined) {
        updates.push(`preferred_randomized = $${paramCount}`);
        values.push(preferred_randomized);
        paramCount++;
      }

      if (updates.length === 0) {
        return NextResponse.json({ error: 'No preferences provided' }, { status: 400 });
      }

      updates.push(`updated_at = NOW()`);
      values.push(userId);

      // Ensure user_progress record exists and update preferences
      await pool.query(
        `INSERT INTO user_progress (id, user_id${preferred_color !== undefined ? ', preferred_color' : ''}${preferred_randomized !== undefined ? ', preferred_randomized' : ''})
         VALUES (gen_random_uuid()::text, $${paramCount}${preferred_color !== undefined ? ', $1' : ''}${preferred_randomized !== undefined ? ', $2' : ''})
         ON CONFLICT (user_id) DO UPDATE
         SET ${updates.join(', ')}`,
        values,
      );

      return NextResponse.json({
        success: true,
        preferred_color: preferred_color || 'blue',
        preferred_randomized: preferred_randomized || false,
      });
    } catch (error: unknown) {
      // Handle case where preferred columns don't exist yet
      if (error instanceof Error && 'code' in error && error.code === '42703') {
        // Try to add missing columns and retry
        try {
          if (preferred_color !== undefined) {
            await pool.query(
              "ALTER TABLE user_progress ADD COLUMN IF NOT EXISTS preferred_color TEXT NOT NULL DEFAULT 'blue'",
            );
          }
          if (preferred_randomized !== undefined) {
            await pool.query(
              'ALTER TABLE user_progress ADD COLUMN IF NOT EXISTS preferred_randomized BOOLEAN NOT NULL DEFAULT false',
            );
          }

          // Retry with simpler update for existing record
          const updates = [];
          const values = [];
          let paramCount = 1;

          if (preferred_color !== undefined) {
            updates.push(`preferred_color = $${paramCount}`);
            values.push(preferred_color);
            paramCount++;
          }
          if (preferred_randomized !== undefined) {
            updates.push(`preferred_randomized = $${paramCount}`);
            values.push(preferred_randomized);
            paramCount++;
          }

          updates.push(`updated_at = NOW()`);
          values.push(userId);

          await pool.query(
            `UPDATE user_progress 
             SET ${updates.join(', ')}
             WHERE user_id = $${paramCount}`,
            values,
          );

          return NextResponse.json({
            success: true,
            preferred_color: preferred_color || 'blue',
            preferred_randomized: preferred_randomized || false,
          });
        } catch (retryError) {
          console.error('Error adding preferred columns:', retryError);
          return NextResponse.json({ error: 'Database schema update failed' }, { status: 500 });
        }
      }
      throw error;
    }
  } catch (error) {
    console.error('Error updating user preferences:', error);
    return NextResponse.json({ error: 'Failed to update user preferences' }, { status: 500 });
  }
}
