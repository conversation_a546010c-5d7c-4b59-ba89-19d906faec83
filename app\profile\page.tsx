'use client';

import { useUser, withPageAuthRequired } from '@auth0/nextjs-auth0';
import { Settings } from 'lucide-react';
import Link from 'next/link';

import { UserDecks } from '@/components/user-decks';
import { StudyProgressCard } from '@/components/study-progress-card';
import { useProgress } from '@/components/progress-provider';
import { Button } from '@/components/ui/button';

function ProfileContent() {
  const { user } = useUser();
  const { activeSession } = useProgress();

  return (
    <div className="container mx-auto max-w-4xl px-4 py-8">
      {/* Welcome Header */}
      <div className="mb-8 text-center relative">
        {activeSession && (
          <div className="absolute top-0 right-0 flex items-center gap-2 bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-3 py-1 rounded-full text-sm font-medium">
            <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
            Active Session
          </div>
        )}
        <h1 className="text-4xl md:text-5xl font-bold font-londrina-sketch">
          Welcome, {user?.name}
        </h1>
        <div className="mt-4 flex justify-center">
          <Link href="/settings">
            <Button variant="outline" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </Button>
          </Link>
        </div>
      </div>

      {/* Study Progress Dashboard */}
      <div className="mb-8">
        <StudyProgressCard />
      </div>

      {/* User Decks Section */}
      <div className="space-y-6">
        <UserDecks />
      </div>
    </div>
  );
}

// Protect the profile page with authentication
export default withPageAuthRequired(ProfileContent, {
  returnTo: '/profile',
});
