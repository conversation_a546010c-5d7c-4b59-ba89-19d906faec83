'use client';

import { useState, useEffect } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useUser } from '@auth0/nextjs-auth0';
import Link from 'next/link';

import { CardConfiguration } from '@/components/card-configuration';
import { FlashcardDisplay } from '@/components/flashcard-display';
import { CardBrowser } from '@/components/card-browser';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Switch } from '@/components/ui/switch';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { useToast } from '@/hooks/use-toast';
import { Share2, <PERSON>, Lock, Loader2, ChevronUp, ChevronDown } from 'lucide-react';
import { showAchievementToasts, showSessionCompleteToast } from '@/lib/toast-utils';

interface CardData {
  id: string;
  data: Record<string, string>;
}

interface Deck {
  id: string;
  name: string;
  description: string;
  columns: string[];
  frontColumns: string[];
  backColumns: string[];
  hasHtml: boolean;
  isPublic?: boolean;
  isOwner?: boolean;
  cardCount: number;
  createdAt: string;
  cards: CardData[];
}

function StudyContent() {
  const searchParams = useSearchParams();
  const deckId = searchParams.get('deck');
  const { user } = useUser();

  const [deck, setDeck] = useState<Deck | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'configure' | 'study' | 'browse'>('configure');
  const [isToggling, setIsToggling] = useState(false);
  const [requiresAuth, setRequiresAuth] = useState(false);
  const [isCardBrowserVisible, setIsCardBrowserVisible] = useState(false);
  const { toast } = useToast();
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  // Study configuration state
  const [isRandomized, setIsRandomized] = useState(false);
  const [cardLimit, setCardLimit] = useState<number | null>(null);
  const [selectedColor, setSelectedColor] = useState('blue');
  const [shuffleSeed, setShuffleSeed] = useState<string>('');

  // Load user preferences on component mount
  useEffect(() => {
    const loadUserPreferences = async () => {
      try {
        // Try to load from authenticated user preferences
        const response = await fetch('/api/user-preferences');
        if (response.ok) {
          const data = await response.json();
          setSelectedColor(data.preferred_color);
          setIsRandomized(data.preferred_randomized);
        } else if (response.status === 401) {
          // User not authenticated, try localStorage fallback
          const savedColor = localStorage.getItem('studyColorPreference');
          const savedRandomized = localStorage.getItem('studyRandomizedPreference');
          if (savedColor) {
            setSelectedColor(savedColor);
          }
          if (savedRandomized !== null) {
            setIsRandomized(savedRandomized === 'true');
          }
        }
      } catch (error) {
        console.error('Error loading user preferences:', error);
        // Fallback to localStorage on error
        const savedColor = localStorage.getItem('studyColorPreference');
        const savedRandomized = localStorage.getItem('studyRandomizedPreference');
        if (savedColor) {
          setSelectedColor(savedColor);
        }
        if (savedRandomized !== null) {
          setIsRandomized(savedRandomized === 'true');
        }
      }
    };

    loadUserPreferences();
  }, []);

  useEffect(() => {
    const fetchDeck = async () => {
      if (!deckId) {
        setError('No deck ID provided');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Use regular deck API - it handles both public and private access
        const response = await fetch(`/api/decks/${deckId}`);

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Deck not found');
          }
          throw new Error('Failed to fetch deck');
        }

        const data = await response.json();

        // Check if deck is public
        if (data.deck.isPublic) {
          // Public deck - allow access for everyone
          setDeck(data.deck);
          setRequiresAuth(false);

          // Record view for public deck only if user is not the owner (non-blocking)
          if (!data.deck.isOwner) {
            recordDeckView(deckId);
          }
        } else {
          // Private deck - check if user has permission
          if (response.status === 401) {
            setRequiresAuth(true);
            throw new Error('You need to be logged in to access this deck');
          } else if (response.status === 403) {
            throw new Error('You do not have permission to access this deck');
          }
          // If we get here, user has permission (and is likely the owner)
          setDeck(data.deck);
          setRequiresAuth(false);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchDeck();
  }, [deckId]);

  // Record deck view for analytics (non-blocking)
  const recordDeckView = async (deckId: string) => {
    try {
      await fetch('/api/deck-views', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ deckId }),
      });
      // We don't need to handle the response, just fire and forget
    } catch (error) {
      // Silently fail - don't interrupt user experience
      console.error('Failed to record deck view:', error);
    }
  };

  const handleConfigurationChange = (randomized: boolean, limit: number | null, color: string) => {
    setIsRandomized(randomized);
    setCardLimit(limit);
    setSelectedColor(color);

    // Save preferences
    savePreferences(color, randomized);
  };

  const savePreferences = async (color: string, randomized: boolean) => {
    try {
      // Try to save to authenticated user preferences
      const response = await fetch('/api/user-preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          preferred_color: color,
          preferred_randomized: randomized,
        }),
      });

      if (!response.ok && response.status === 401) {
        // User not authenticated, save to localStorage
        localStorage.setItem('studyColorPreference', color);
        localStorage.setItem('studyRandomizedPreference', randomized.toString());
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
      // Fallback to localStorage on error
      localStorage.setItem('studyColorPreference', color);
      localStorage.setItem('studyRandomizedPreference', randomized.toString());
    }
  };

  const handleStartStudying = () => {
    // Generate a new shuffle seed to ensure cards are reshuffled
    if (isRandomized) {
      setShuffleSeed(Math.random().toString(36).substring(2, 15));
    }
    setStep('study');
  };

  const handleBackToConfigure = () => {
    setStep('configure');
  };

  const handleBrowseCards = () => {
    setIsCardBrowserVisible(!isCardBrowserVisible);
  };

  const handleBackToProfile = () => {
    // This will be handled by the back button in CardConfiguration
  };

  const handlePublicToggle = async (isPublic: boolean) => {
    if (!deckId || isToggling) return;

    setIsToggling(true);

    try {
      const response = await fetch(`/api/decks/${deckId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isPublic }),
      });

      if (!response.ok) {
        throw new Error('Failed to update deck');
      }

      // Update local state
      setDeck((prevDeck) =>
        prevDeck
          ? {
              ...prevDeck,
              isPublic,
            }
          : null,
      );

      toast({
        title: isPublic ? 'Deck is now public' : 'Deck is now private',
        description: isPublic
          ? 'Your deck is now visible to everyone'
          : 'Your deck is now private and only visible to you',
        duration: 3000,
      });
    } catch (error) {
      console.error('Error updating deck:', error);
      toast({
        title: 'Error',
        description: 'Failed to update deck visibility',
        variant: 'destructive',
        duration: 4000,
      });
    } finally {
      setIsToggling(false);
    }
  };

  const copyShareLink = async () => {
    if (!deck?.id) return;

    const shareUrl = `${window.location.origin}/study?deck=${deck.id}`;
    try {
      await navigator.clipboard.writeText(shareUrl);
      toast({
        title: 'Link copied!',
        description: 'Share link has been copied to clipboard',
        duration: 3000,
      });
    } catch (error) {
      console.error('Failed to copy link:', error);
      toast({
        title: 'Error',
        description: 'Failed to copy link to clipboard',
        variant: 'destructive',
        duration: 4000,
      });
    }
  };

  const handleDelete = async () => {
    setIsDeleting(true);
    try {
      const response = await fetch(`/api/decks/${deckId}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        throw new Error('Failed to delete deck');
      }
      toast({
        title: 'Deck deleted',
        description: 'The deck has been successfully deleted',
        duration: 3000,
      });
      router.push('/profile');
    } catch (error) {
      console.error('Error deleting deck:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete the deck',
        variant: 'destructive',
        duration: 4000,
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
    }
  };

  if (loading) {
    return (
      <main className="bg-background">
        <div className="container mx-auto max-w-4xl px-4 py-8">
          {/* Top Navigation Bar - Loading State */}
          <div className="mb-6 pb-4 border-b">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="min-w-0 flex-1">
                <Skeleton className="h-5 sm:h-6 w-32 sm:w-48 mb-2" />
                <Skeleton className="h-3 sm:h-4 w-24 sm:w-32" />
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                <Skeleton className="h-5 sm:h-6 w-16 sm:w-20" />
                <Skeleton className="h-4 sm:h-5 w-20 sm:w-24" />
              </div>
            </div>
          </div>

          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-48" />
              <Skeleton className="h-4 w-32" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-32 w-full" />
            </CardContent>
          </Card>
        </div>
      </main>
    );
  }

  if (error || !deck) {
    return (
      <main className="bg-background">
        <div className="container mx-auto max-w-4xl px-4 py-8">
          {/* Top Navigation Bar - Error State */}
          <div className="mb-6 pb-4 border-b">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="min-w-0 flex-1">
                <h1 className="text-xl sm:text-2xl font-bold text-muted-foreground">
                  Study Session
                </h1>
                <p className="text-muted-foreground text-xs sm:text-sm mt-1">Unable to load deck</p>
              </div>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>{requiresAuth ? 'Authentication Required' : 'Error'}</CardTitle>
              <CardDescription>
                {requiresAuth
                  ? 'You need to be logged in to access this private deck.'
                  : error || 'Deck not found'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {requiresAuth ? (
                <div className="flex gap-2">
                  <Button asChild>
                    <Link
                      href={`/api/auth/login?returnTo=${encodeURIComponent(window.location.pathname + window.location.search)}`}
                    >
                      Log In
                    </Link>
                  </Button>
                  <Button variant="outline" asChild>
                    <Link href="/profile">Back to Profile</Link>
                  </Button>
                </div>
              ) : (
                <Button asChild>
                  <Link href="/profile">Back to Profile</Link>
                </Button>
              )}
            </CardContent>
          </Card>
        </div>
      </main>
    );
  }

  return (
    <main className="bg-background">
      <div className="container mx-auto max-w-4xl px-4 py-8">
        {/* Top Navigation Bar - Mobile Optimized */}
        <div className="mb-6 pb-4 border-b">
          {/* Mobile: Stack vertically | Desktop: Horizontal layout */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            {/* Left section: Deck info */}
            <div className="min-w-0 flex-1">
              <h1 className="text-xl sm:text-2xl font-bold truncate pr-2">{deck.name}</h1>
              {deck.description && (
                <p className="text-muted-foreground text-sm md:text-md mt-1 line-clamp-3">
                  {deck.description}
                </p>
              )}
              <p className="text-muted-foreground text-sm mt-2">
                Created {new Date(deck.createdAt).toLocaleDateString()}
              </p>
            </div>

            {/* Right section: Public/Private Toggle (only for deck owners) */}
            {deck.isOwner && (
              <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-muted-foreground" />
                  {isToggling ? (
                    <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                  ) : (
                    <Switch
                      checked={deck.isPublic || false}
                      onCheckedChange={handlePublicToggle}
                      disabled={isToggling}
                      aria-label={`Make deck ${deck.isPublic ? 'private' : 'public'}`}
                    />
                  )}
                  <Globe className="h-4 w-4 text-muted-foreground" />
                </div>

                {deck.isPublic && !isToggling && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyShareLink}
                    className="flex items-center gap-1 text-xs"
                  >
                    <Share2 className="h-3 w-3" />
                    Copy Link
                  </Button>
                )}
              </div>
            )}

            {/* Show public indicator for non-owners viewing public decks */}
            {!deck.isOwner && deck.isPublic && (
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Globe className="h-4 w-4" />
                <span>Public deck</span>
              </div>
            )}
          </div>
        </div>

        {step === 'configure' && (
          <>
            {/* Toggle Button */}
            <div className="flex justify-start mb-6">
              <Button
                variant="outline"
                onClick={handleBrowseCards}
                className="flex items-center gap-2 px-6"
                size="default"
              >
                {isCardBrowserVisible ? (
                  <>
                    <ChevronUp className="h-4 w-4" />
                    Hide Cards
                  </>
                ) : (
                  <>
                    <ChevronDown className="h-4 w-4" />
                    Browse Cards
                  </>
                )}
              </Button>
            </div>

            {/* Card Browser Section */}
            {isCardBrowserVisible && (
              <div className="mb-6 transition-all duration-300 ease-in-out">
                <CardBrowser cards={deck.cards} columns={deck.columns} />
              </div>
            )}

            {/* Card Configuration Section */}
            <CardConfiguration
              totalCards={deck.cardCount}
              isRandomized={isRandomized}
              cardLimit={cardLimit}
              selectedColor={selectedColor}
              onConfigurationChange={handleConfigurationChange}
              onStartStudying={handleStartStudying}
              onBackToUpload={handleBackToProfile}
              showBackButton={false}
              showDeleteButton={deck.isOwner}
              onDelete={() => setShowDeleteDialog(true)}
              isDeleting={isDeleting}
            />
          </>
        )}

        {step === 'study' && (
          <FlashcardDisplay
            data={deck.cards.map((card) => card.data)}
            frontColumns={deck.frontColumns}
            backColumns={deck.backColumns}
            isRandomized={isRandomized}
            cardLimit={cardLimit}
            selectedColor={selectedColor}
            hasHtml={deck.hasHtml}
            deckId={deck.id}
            deckName={deck.name}
            isLoggedIn={!!user}
            shuffleSeed={shuffleSeed}
            onBackToConfigure={handleBackToConfigure}
            onSessionComplete={(result) => {
              // Handle session completion with consistent toast notifications
              console.log('Session completed:', result);

              // Show achievement notifications if any
              if (result.achievements && result.achievements.length > 0) {
                showAchievementToasts(result.achievements, toast);
              }

              // Show session completion notification
              showSessionCompleteToast(
                result.cardsStudied,
                result.durationSeconds,
                (seconds) => {
                  const minutes = Math.floor(seconds / 60);
                  const remainingSeconds = seconds % 60;
                  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
                },
                toast,
              );
            }}
          />
        )}

        {step === 'browse' && <CardBrowser cards={deck.cards} columns={deck.columns} />}
      </div>

      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Deck</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this deck? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete} disabled={isDeleting}>
              {isDeleting ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </main>
  );
}
// Wrapper component that conditionally applies authentication
function StudyPageWrapper() {
  return <StudyContent />;
}

function StudyPage() {
  // Authentication is now handled by the component itself, not middleware
  return <StudyPageWrapper />;
}

export default StudyPage;
