import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

interface LegalPageLayoutProps {
  title: string;
  lastUpdated: string;
  children: React.ReactNode;
}

export function LegalPageLayout({ title, lastUpdated, children }: LegalPageLayoutProps) {
  return (
    <main className="bg-background min-h-screen">
      <div className="container mx-auto max-w-4xl px-4 pt-8 pb-16 sm:py-8">
        <div className="mb-8 text-center">
          <h1
            className="text-foreground mb-2 text-5xl font-bold text-balance"
            style={{ fontFamily: 'var(--font-londrina-sketch)' }}
          >
            {title}
          </h1>
          <p className="text-muted-foreground text-lg">Last updated: {lastUpdated}</p>
        </div>

        <Card className="border-border/50 bg-card/50 backdrop-blur supports-[backdrop-filter]:bg-card/50">
          <CardHeader>
            <CardTitle className="text-2xl font-semibold tracking-tight">{title}</CardTitle>
            <Separator />
          </CardHeader>
          <CardContent className="prose prose-neutral dark:prose-invert max-w-none">
            {children}
          </CardContent>
        </Card>
      </div>
    </main>
  );
}
