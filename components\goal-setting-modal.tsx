'use client';

import React, { useState, useEffect } from 'react';
import { XIcon } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { Target, Calendar, Zap, Minus, Plus } from 'lucide-react';
import { useIsMobile } from '@/components/ui/use-mobile';

interface GoalSettingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (goals: StudyGoals) => void;
  currentGoals?: StudyGoals;
}

interface StudyGoals {
  dailyCards: number;
  weeklyCards: number;
  weeklyStreak: number;
}

export function GoalSettingModal({ isOpen, onClose, onSave, currentGoals }: GoalSettingModalProps) {
  const isMobile = useIsMobile();
  const [goals, setGoals] = useState<StudyGoals>(
    currentGoals || {
      dailyCards: 50,
      weeklyCards: 300,
      weeklyStreak: 5,
    },
  );

  // Auto-calculate weekly cards when daily cards change
  useEffect(() => {
    setGoals((prev) => ({ ...prev, weeklyCards: prev.dailyCards * 7 }));
  }, [goals.dailyCards]);

  const adjustValue = (field: keyof StudyGoals, increment: number) => {
    setGoals((prev) => {
      const newValue = Math.max(1, prev[field] + increment);
      const maxValues = {
        dailyCards: 200,
        weeklyCards: 1000,
        weeklyStreak: 7,
      };
      return {
        ...prev,
        [field]: Math.min(newValue, maxValues[field]),
      };
    });
  };

  const handleSave = () => {
    onSave(goals);
    onClose();
  };

  if (!isOpen) return null;

  const modalContent = (
    <div className={`${isMobile ? 'space-y-4' : 'space-y-6'}`}>
      {/* Custom Goals */}
      <div className={`${isMobile ? 'space-y-4' : 'space-y-6'}`}>
        <div
          className={`relative overflow-hidden rounded-xl ${isMobile ? 'p-4' : 'p-6'} bg-gradient-to-br from-amber-50 to-orange-50 dark:from-amber-950/30 dark:to-orange-950/30 border border-amber-100 dark:border-amber-900/50 shadow-sm transition-all duration-300 hover:shadow-md`}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-amber-500/5 to-orange-500/5 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative z-10">
            <Label
              htmlFor="dailyCards"
              className={`flex items-center gap-2 ${isMobile ? 'text-sm font-medium mb-3' : 'mb-4'} text-amber-800 dark:text-amber-200`}
            >
              <Zap className="h-4 w-4 text-amber-500" />
              Daily Cards Target
            </Label>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="h-10 w-10 p-0 rounded-full border-amber-200 dark:border-amber-800 bg-white/80 dark:bg-black/20 hover:bg-amber-50 dark:hover:bg-amber-900/20 transition-colors"
                onClick={() => adjustValue('dailyCards', -5)}
                disabled={goals.dailyCards <= 1}
              >
                <Minus className="h-3 w-3 text-amber-600 dark:text-amber-300" />
              </Button>
              <Input
                id="dailyCards"
                type="number"
                min="1"
                max="200"
                value={goals.dailyCards}
                onChange={(e) => {
                  setGoals((prev) => ({ ...prev, dailyCards: parseInt(e.target.value) || 0 }));
                }}
                className={`text-center flex-1 font-semibold ${isMobile ? 'text-lg h-10' : 'text-xl h-12'} bg-white/80 dark:bg-black/20 border-amber-200 dark:border-amber-800 rounded-lg`}
              />
              <Button
                variant="outline"
                size="sm"
                className="h-10 w-10 p-0 rounded-full border-amber-200 dark:border-amber-800 bg-white/80 dark:bg-black/20 hover:bg-amber-50 dark:hover:bg-amber-900/20 transition-colors"
                onClick={() => adjustValue('dailyCards', 5)}
                disabled={goals.dailyCards >= 200}
              >
                <Plus className="h-3 w-3 text-amber-600 dark:text-amber-300" />
              </Button>
            </div>
            <p
              className={`mt-3 text-amber-700 dark:text-amber-300 ${isMobile ? 'text-center text-xs' : 'text-xs'}`}
            >
              Recommended: 20-100 cards per day
            </p>
          </div>
        </div>

        <div
          className={`relative overflow-hidden rounded-xl ${isMobile ? 'p-4' : 'p-6'} bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border border-blue-100 dark:border-blue-900/50 shadow-sm transition-all duration-300 hover:shadow-md`}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative z-10">
            <Label
              htmlFor="weeklyCards"
              className={`flex items-center gap-2 ${isMobile ? 'text-sm font-medium mb-3' : 'mb-4'} text-blue-800 dark:text-blue-200`}
            >
              <Calendar className="h-4 w-4 text-blue-500" />
              Weekly Cards Target
            </Label>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="h-10 w-10 p-0 rounded-full border-blue-200 dark:border-blue-800 bg-white/80 dark:bg-black/20 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                onClick={() => adjustValue('weeklyCards', -25)}
                disabled={goals.weeklyCards <= 1}
              >
                <Minus className="h-3 w-3 text-blue-600 dark:text-blue-300" />
              </Button>
              <Input
                id="weeklyCards"
                type="number"
                min="1"
                max="1000"
                value={goals.weeklyCards}
                onChange={(e) => {
                  setGoals((prev) => ({ ...prev, weeklyCards: parseInt(e.target.value) || 0 }));
                }}
                className={`text-center flex-1 font-semibold ${isMobile ? 'text-lg h-10' : 'text-xl h-12'} bg-white/80 dark:bg-black/20 border-blue-200 dark:border-blue-800 rounded-lg`}
              />
              <Button
                variant="outline"
                size="sm"
                className="h-10 w-10 p-0 rounded-full border-blue-200 dark:border-blue-800 bg-white/80 dark:bg-black/20 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors"
                onClick={() => adjustValue('weeklyCards', 25)}
                disabled={goals.weeklyCards >= 1000}
              >
                <Plus className="h-3 w-3 text-blue-600 dark:text-blue-300" />
              </Button>
            </div>
            <p
              className={`mt-3 text-blue-700 dark:text-blue-300 ${isMobile ? 'text-center text-xs' : 'text-xs'}`}
            >
              Auto-calculated: {goals.dailyCards * 7} based on daily goal
            </p>
          </div>
        </div>

        <div
          className={`relative overflow-hidden rounded-xl ${isMobile ? 'p-4' : 'p-6'} bg-gradient-to-br from-emerald-50 to-green-50 dark:from-emerald-950/30 dark:to-green-950/30 border border-emerald-100 dark:border-emerald-900/50 shadow-sm transition-all duration-300 hover:shadow-md`}
        >
          <div className="absolute inset-0 bg-gradient-to-r from-emerald-500/5 to-green-500/5 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative z-10">
            <Label
              htmlFor="weeklyStreak"
              className={`flex items-center gap-2 ${isMobile ? 'text-sm font-medium mb-3' : 'mb-4'} text-emerald-800 dark:text-emerald-200`}
            >
              <Target className="h-4 w-4 text-emerald-500" />
              Days Per Week Target
            </Label>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="h-10 w-10 p-0 rounded-full border-emerald-200 dark:border-emerald-800 bg-white/80 dark:bg-black/20 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-colors"
                onClick={() => adjustValue('weeklyStreak', -1)}
                disabled={goals.weeklyStreak <= 1}
              >
                <Minus className="h-3 w-3 text-emerald-600 dark:text-emerald-300" />
              </Button>
              <Input
                id="weeklyStreak"
                type="number"
                min="1"
                max="7"
                value={goals.weeklyStreak}
                onChange={(e) => {
                  setGoals((prev) => ({ ...prev, weeklyStreak: parseInt(e.target.value) || 0 }));
                }}
                className={`text-center flex-1 font-semibold ${isMobile ? 'text-lg h-10' : 'text-xl h-12'} bg-white/80 dark:bg-black/20 border-emerald-200 dark:border-emerald-800 rounded-lg`}
              />
              <Button
                variant="outline"
                size="sm"
                className="h-10 w-10 p-0 rounded-full border-emerald-200 dark:border-emerald-800 bg-white/80 dark:bg-black/20 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-colors"
                onClick={() => adjustValue('weeklyStreak', 1)}
                disabled={goals.weeklyStreak >= 7}
              >
                <Plus className="h-3 w-3 text-emerald-600 dark:text-emerald-300" />
              </Button>
            </div>
            <p
              className={`mt-3 text-emerald-700 dark:text-emerald-300 ${isMobile ? 'text-center text-xs' : 'text-xs'}`}
            >
              Consistency is key! Aim for 3-7 days per week
            </p>
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className={`flex gap-3 ${isMobile ? 'pt-4 border-t' : ''}`}>
        <Button
          onClick={onClose}
          variant="outline"
          className={`flex-1 ${isMobile ? 'h-10 text-sm' : 'h-12'} rounded-lg border-slate-200 dark:border-slate-800 bg-white/80 dark:bg-black/20 hover:bg-slate-50 dark:hover:bg-slate-900/20 transition-colors`}
        >
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          className={`flex-1 ${isMobile ? 'h-10 text-sm' : 'h-12'} rounded-lg bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-md hover:shadow-lg transition-all duration-300 transform hover:scale-[1.02]`}
        >
          Save Goals
        </Button>
      </div>
    </div>
  );

  if (isMobile) {
    return (
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent
          side="bottom"
          className="rounded-t-2xl border-t bg-gradient-to-b from-background to-background/95 backdrop-blur-lg max-h-[85vh] overflow-y-auto p-4"
        >
          <SheetHeader className="text-left pb-4">
            <SheetTitle className="flex items-center gap-2 text-lg font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              <Target className="h-5 w-5 text-blue-500" />
              Set Your Study Goals
            </SheetTitle>
          </SheetHeader>
          <div className="pt-2">{modalContent}</div>
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        showCloseButton={false}
        className="max-w-lg w-full bg-gradient-to-br from-background to-background/95 backdrop-blur-lg border border-slate-200 dark:border-slate-800 rounded-2xl shadow-xl p-0 overflow-hidden"
      >
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-50"></div>
        <div className="relative z-10">
          <DialogHeader className="p-6 pb-4 border-b border-slate-100 dark:border-slate-800">
            <DialogTitle className="flex items-center gap-2 text-xl font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              <Target className="h-5 w-5 text-blue-500" />
              Set Your Study Goals
            </DialogTitle>
            <DialogClose className="ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4">
              <XIcon className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </DialogClose>
          </DialogHeader>
          <div className="p-6">{modalContent}</div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
