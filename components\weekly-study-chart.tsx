'use client';

import React from 'react';
import { Bar, BarChart, CartesianGrid, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';
import { ChartContainer, ChartTooltipContent, ChartConfig } from '@/components/ui/chart';

interface Session {
  deckName: string;
  date: Date;
  cardsStudied: number;
  duration: number;
}

interface WeeklyStudyChartProps {
  recentSessions: Session[];
}

const chartConfig: ChartConfig = {
  cards: {
    label: 'Cards Studied',
    theme: {
      light: 'hsl(221.2 83.2% 53.3%)', // blue-600
      dark: 'hsl(217.2 91.2% 59.8%)', // blue-500
    },
  },
};

export function WeeklyStudyChart({ recentSessions }: WeeklyStudyChartProps) {
  // Process data to get cards studied per day for the last 7 days
  const processWeeklyData = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize today to start of day

    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date(today);
      date.setDate(today.getDate() - (6 - i));
      return date;
    });

    const dailyData = last7Days.map((date) => {
      const dayName = date.toLocaleDateString('en-US', { weekday: 'short' });

      // Create date range for this day (midnight to midnight)
      const dayStart = new Date(date);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(date);
      dayEnd.setHours(23, 59, 59, 999);

      const cardsStudied = recentSessions
        .filter((session) => {
          const sessionDate = new Date(session.date);
          return sessionDate >= dayStart && sessionDate <= dayEnd;
        })
        .reduce((sum, session) => sum + session.cardsStudied, 0);

      return {
        day: dayName,
        cards: cardsStudied,
        date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      };
    });

    return dailyData;
  };

  const data = processWeeklyData();

  // Debug log to help verify data processing
  console.log('Weekly Study Chart Debug:', {
    recentSessions: recentSessions.map((s) => ({
      date: s.date,
      cardsStudied: s.cardsStudied,
      dateString: new Date(s.date).toISOString(),
    })),
    processedData: data,
  });

  return (
    <div className="w-full">
      <ChartContainer config={chartConfig} className="h-[200px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={data} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis
              dataKey="day"
              tickLine={false}
              axisLine={false}
              tick={{ fontSize: 12 }}
              tickMargin={10}
            />
            <YAxis
              tickLine={false}
              axisLine={false}
              tick={{ fontSize: 12 }}
              tickMargin={10}
              allowDecimals={false}
            />
            <Tooltip
              content={<ChartTooltipContent />}
              cursor={{ fill: 'rgba(59, 130, 246, 0.1)' }}
            />
            <Bar dataKey="cards" fill="var(--color-cards)" radius={[4, 4, 0, 0]} strokeWidth={0} />
          </BarChart>
        </ResponsiveContainer>
      </ChartContainer>
    </div>
  );
}
