import { useToast } from '@/hooks/use-toast';

export interface Achievement {
  name: string;
  description: string;
}

export interface SessionResult {
  cardsStudied: number;
  durationSeconds: number;
  achievements?: Achievement[];
}

/**
 * Shows achievement unlocked toast notifications
 */
export const showAchievementToasts = (
  achievements: Achievement[],
  toast: ReturnType<typeof useToast>['toast'],
) => {
  // Emit custom event for achievements
  const achievementEvent = new CustomEvent('newAchievements', {
    detail: { achievements },
  });
  window.dispatchEvent(achievementEvent);

  // Show individual achievement toasts
  achievements.forEach((achievement) => {
    toast({
      title: `🏆 Achievement Unlocked!`,
      description: `${achievement.name}: ${achievement.description}`,
      duration: 5000,
    });
  });
};

/**
 * Shows session completion toast notification
 */
export const showSessionCompleteToast = (
  cardsStudied: number,
  durationSeconds: number,
  formatElapsedTime: (seconds: number) => string,
  toast: ReturnType<typeof useToast>['toast'],
) => {
  toast({
    title: '🎉 Study Session Complete!',
    description: `You studied ${cardsStudied} cards in ${formatElapsedTime(durationSeconds)}`,
    duration: 5000,
  });
};

/**
 * Handles session completion with achievements and toast notifications
 */
export const handleSessionCompletion = async (
  sessionResult: SessionResult,
  formatElapsedTime: (seconds: number) => string,
  toast: ReturnType<typeof useToast>['toast'],
  onSessionComplete?: (result: SessionResult) => void,
) => {
  // Show achievement notifications if any
  if (sessionResult.achievements && sessionResult.achievements.length > 0) {
    showAchievementToasts(sessionResult.achievements, toast);
  }

  // Show session completion notification
  showSessionCompleteToast(
    sessionResult.cardsStudied,
    sessionResult.durationSeconds,
    formatElapsedTime,
    toast,
  );

  // Call optional callback
  if (onSessionComplete) {
    onSessionComplete(sessionResult);
  }
};

/**
 * Shows a success toast notification with consistent formatting
 */
export const showSuccessToast = (
  title: string,
  description: string,
  toast: ReturnType<typeof useToast>['toast'],
  duration: number = 3000,
) => {
  toast({
    title,
    description,
    duration,
  });
};

/**
 * Shows an error toast notification with consistent formatting
 */
export const showErrorToast = (
  title: string,
  description: string,
  toast: ReturnType<typeof useToast>['toast'],
  duration: number = 4000,
) => {
  toast({
    title,
    description,
    variant: 'destructive',
    duration,
  });
};

/**
 * Shows an info toast notification with consistent formatting
 */
export const showInfoToast = (
  title: string,
  description: string,
  toast: ReturnType<typeof useToast>['toast'],
  duration: number = 3000,
) => {
  toast({
    title,
    description,
    duration,
  });
};
