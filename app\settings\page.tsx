'use client';

import { useState, useEffect, useRef } from 'react';
import { useUser } from '@auth0/nextjs-auth0';
import { useTheme } from 'next-themes';
import { Settings, Palette, Moon, Sun, Monitor, Shuffle, User, Lock } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { colorOptions } from '@/lib/color-options';

interface UserPreferences {
  preferred_color: string;
  preferred_randomized: boolean;
}

function SettingsContent() {
  const { user } = useUser();
  const { theme, setTheme } = useTheme();
  const { toast } = useToast();

  const [preferences, setPreferences] = useState<UserPreferences>({
    preferred_color: 'blue',
    preferred_randomized: false,
  });
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);
  const [hasLoadedPreferences, setHasLoadedPreferences] = useState(false);
  const saveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle theme hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Load user preferences
  useEffect(() => {
    const loadPreferences = async () => {
      try {
        const response = await fetch('/api/user-preferences');
        if (response.ok) {
          const data = await response.json();
          setPreferences({
            preferred_color: data.preferred_color || 'blue',
            preferred_randomized: data.preferred_randomized || false,
          });
        } else if (response.status === 401) {
          // User not authenticated, try localStorage fallback
          const savedColor = localStorage.getItem('studyColorPreference');
          const savedRandomized = localStorage.getItem('studyRandomizedPreference');
          setPreferences({
            preferred_color: savedColor || 'blue',
            preferred_randomized: savedRandomized === 'true',
          });
        }
      } catch (error) {
        console.error('Error loading preferences:', error);
        // Fallback to localStorage
        const savedColor = localStorage.getItem('studyColorPreference');
        const savedRandomized = localStorage.getItem('studyRandomizedPreference');
        setPreferences({
          preferred_color: savedColor || 'blue',
          preferred_randomized: savedRandomized === 'true',
        });
      } finally {
        setLoading(false);
        setHasLoadedPreferences(true);
      }
    };

    loadPreferences();
  }, []);

  // Track previous preferences to compare changes
  const [prevPreferences, setPrevPreferences] = useState<UserPreferences | null>(null);

  // Auto-save preferences with debounce
  useEffect(() => {
    // Don't save on initial mount or if preferences haven't loaded yet
    if (loading || !hasLoadedPreferences) return;

    // Don't save if this is the first time setting prevPreferences (initial load)
    if (!prevPreferences) {
      setPrevPreferences({ ...preferences });
      return;
    }

    // Don't save if preferences haven't actually changed
    if (JSON.stringify(preferences) === JSON.stringify(prevPreferences)) return;

    // Clear existing timeout
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    // Set new timeout
    saveTimeoutRef.current = setTimeout(async () => {
      try {
        const response = await fetch('/api/user-preferences', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(preferences),
        });

        if (response.ok) {
          toast({
            title: 'Settings saved',
            description: 'Your preferences have been saved.',
            duration: 3000,
          });
        } else if (response.status === 401) {
          // Save to localStorage for non-authenticated users
          localStorage.setItem('studyColorPreference', preferences.preferred_color);
          localStorage.setItem(
            'studyRandomizedPreference',
            preferences.preferred_randomized.toString(),
          );
          toast({
            title: 'Settings saved locally',
            description: 'Your preferences have been saved.',
            duration: 3000,
          });
        } else {
          throw new Error('Failed to save preferences');
        }
        // Update prevPreferences after successful save
        setPrevPreferences({ ...preferences });
      } catch (error) {
        console.error('Error saving preferences:', error);
        // Fallback to localStorage
        localStorage.setItem('studyColorPreference', preferences.preferred_color);
        localStorage.setItem(
          'studyRandomizedPreference',
          preferences.preferred_randomized.toString(),
        );
        toast({
          title: 'Settings saved locally',
          description: 'Your preferences have been saved.',
          duration: 3000,
        });
        // Update prevPreferences after successful save
        setPrevPreferences({ ...preferences });
      }
    }, 1000); // Debounce for 1 second

    // Cleanup timeout on unmount
    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, [preferences, loading, hasLoadedPreferences, prevPreferences, toast]);

  if (loading) {
    return (
      <main className="bg-background">
        <div className="container mx-auto max-w-4xl px-4 py-8">
          <div className="mb-8 text-center">
            <h1 className="text-4xl md:text-5xl font-bold font-londrina-sketch mb-2">Settings</h1>
            <p className="text-muted-foreground text-lg">Customize your experience</p>
          </div>

          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  <CardTitle>Loading...</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="h-16 bg-muted animate-pulse rounded-lg"></div>
                  <div className="h-16 bg-muted animate-pulse rounded-lg"></div>
                  <div className="h-16 bg-muted animate-pulse rounded-lg"></div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    );
  }

  return (
    <main className="bg-background">
      <div className="container mx-auto max-w-4xl px-4 py-8">
        {/* Header */}
        <div className="mb-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold font-londrina-sketch mb-2">Settings</h1>
          <p className="text-muted-foreground text-lg">Customize your experience</p>
        </div>

        <div className="space-y-6">
          {/* Appearance Settings */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Palette className="h-5 w-5" />
                <CardTitle>Appearance</CardTitle>
              </div>
              <CardDescription>Customize the look and feel of your study sessions</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Theme Selection */}
              <div className="space-y-3">
                <Label className="text-base font-medium">Theme</Label>
                <div className="flex gap-2">
                  {mounted && (
                    <>
                      <Button
                        variant={theme === 'light' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setTheme('light')}
                        className="flex items-center gap-2"
                      >
                        <Sun className="h-4 w-4" />
                        Light
                      </Button>
                      <Button
                        variant={theme === 'dark' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setTheme('dark')}
                        className="flex items-center gap-2"
                      >
                        <Moon className="h-4 w-4" />
                        Dark
                      </Button>
                      <Button
                        variant={theme === 'system' ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => setTheme('system')}
                        className="flex items-center gap-2"
                      >
                        <Monitor className="h-4 w-4" />
                        System
                      </Button>
                    </>
                  )}
                </div>
              </div>

              <Separator />

              {/* Color Theme Selection */}
              <div className="space-y-3">
                <Label className="text-base font-medium">Flashcard Color Theme</Label>
                <p className="text-sm text-muted-foreground">
                  Choose your preferred color theme for flashcards
                </p>
                <Select
                  value={preferences.preferred_color}
                  onValueChange={(value) =>
                    setPreferences((prev) => ({ ...prev, preferred_color: value }))
                  }
                >
                  <SelectTrigger className="w-full max-w-xs">
                    <div className="flex items-center gap-2">
                      <div
                        className={`h-4 w-4 rounded-full ${
                          colorOptions.find((c) => c.value === preferences.preferred_color)
                            ?.selectClass || 'bg-blue-500'
                        }`}
                      />
                      <SelectValue />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    {colorOptions.map((color) => (
                      <SelectItem key={color.value} value={color.value}>
                        <div className="flex items-center gap-2">
                          <div className={`h-4 w-4 rounded-full ${color.selectClass}`} />
                          {color.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Study Preferences */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Shuffle className="h-5 w-5" />
                <CardTitle>Study Preferences</CardTitle>
              </div>
              <CardDescription>Configure your default study session settings</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Randomize Cards */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className="text-base font-medium">Randomize Cards by Default</Label>
                  <p className="text-sm text-muted-foreground">
                    Shuffle flashcards in a random order when starting new study sessions
                  </p>
                </div>
                <Switch
                  checked={preferences.preferred_randomized}
                  onCheckedChange={(checked) =>
                    setPreferences((prev) => ({ ...prev, preferred_randomized: checked }))
                  }
                />
              </div>
            </CardContent>
          </Card>

          {/* Account Information */}
          {user && (
            <Card>
              <CardHeader>
                <div className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  <CardTitle>Account Information</CardTitle>
                </div>
                <CardDescription>Your account details and login information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">Name</Label>
                  <p className="text-base">{user.name}</p>
                </div>
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-muted-foreground">Email</Label>
                  <p className="text-base">{user.email}</p>
                </div>
                {user.picture && (
                  <div className="space-y-2">
                    <Label className="text-sm font-medium text-muted-foreground">
                      Profile Picture
                    </Label>
                    <img
                      src={user.picture}
                      alt="Profile"
                      className="h-12 w-12 rounded-full border"
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Privacy & Security */}
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Lock className="h-5 w-5" />
                <CardTitle>Privacy & Security</CardTitle>
              </div>
              <CardDescription>Information about your data and privacy</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-muted-foreground space-y-2">
                <p>
                  • Your study preferences are{' '}
                  {user ? 'stored securely in your account' : 'saved locally in your browser'}
                </p>
                <p>
                  • Your flashcard data is{' '}
                  {user ? 'encrypted and stored securely' : 'processed locally in your browser'}
                </p>
                <p>• We do not share your personal data with third parties</p>
              </div>
              <div className="flex gap-2 pt-2">
                <Button variant="outline" size="sm" asChild>
                  <a href="/privacy-policy">Privacy Policy</a>
                </Button>
                <Button variant="outline" size="sm" asChild>
                  <a href="/terms-of-service">Terms of Service</a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </main>
  );
}

// Protect the settings page with authentication requirement, but allow fallback
function SettingsPage() {
  return <SettingsContent />;
}

export default SettingsPage;
