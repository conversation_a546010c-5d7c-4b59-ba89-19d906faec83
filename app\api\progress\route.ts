import { NextRequest, NextResponse } from 'next/server';

import { auth0 } from '@/lib/auth0';
import pool from '@/lib/db';

// Helper function to get start of day in a specific timezone
function getStartOfDayInTimezone(date: Date, timezone: string): Date {
  const formatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: timezone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });

  const parts = formatter.formatToParts(date);
  const year = parseInt(parts.find((p) => p.type === 'year')?.value || '0');
  const month = parseInt(parts.find((p) => p.type === 'month')?.value || '0') - 1; // JS months are 0-indexed
  const day = parseInt(parts.find((p) => p.type === 'day')?.value || '0');

  // Create a new date in the specified timezone at 00:00:00
  return new Date(Date.UTC(year, month, day, 0, 0, 0));
}

// Helper function to get end of day in a specific timezone
function getEndOfDayInTimezone(date: Date, timezone: string): Date {
  const startOfDay = getStartOfDayInTimezone(date, timezone);
  // Add 24 hours and subtract 1 millisecond to get end of day
  return new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000 - 1);
}

// Helper function to format date as YYYY-MM-DD in a specific timezone
function formatDateInTimezone(date: Date, timezone: string): string {
  const formatter = new Intl.DateTimeFormat('en-CA', {
    timeZone: timezone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  });
  return formatter.format(date);
}

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const session = await auth0.getSession(request);
    if (!session?.user?.sub) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    const userId = session.user.sub;

    // Get user's timezone from header, default to UTC if not provided
    const userTimezone = request.headers.get('X-User-Timezone') || 'UTC';

    // Get user progress data
    const progressResult = await pool.query('SELECT * FROM user_progress WHERE user_id = $1', [
      userId,
    ]);

    let progress = null;
    if (progressResult.rows.length > 0) {
      progress = progressResult.rows[0];
    } else {
      // Initialize progress record if it doesn't exist
      try {
        const initResult = await pool.query(
          `INSERT INTO user_progress (id, user_id)
           VALUES (gen_random_uuid()::text, $1)
           RETURNING *`,
          [userId],
        );
        progress = initResult.rows[0];
      } catch (insertError: unknown) {
        // Handle duplicate key violation (race condition)
        if (insertError instanceof Error && 'code' in insertError && insertError.code === '23505') {
          // Re-fetch the existing record
          const existingResult = await pool.query(
            'SELECT * FROM user_progress WHERE user_id = $1',
            [userId],
          );
          if (existingResult.rows.length > 0) {
            progress = existingResult.rows[0];
          }
        } else {
          throw insertError;
        }
      }
    }

    // Get all study sessions from the last 7 days - timezone based
    const sevenDaysAgo = getStartOfDayInTimezone(new Date(), userTimezone);
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const sessionsResult = await pool.query(
      `SELECT
        ss.*,
        d.name as deck_name
       FROM study_sessions ss
       JOIN decks d ON ss.deck_id = d.id
       WHERE ss.user_id = $1 AND ss.created_at >= $2
       ORDER BY ss.created_at DESC`,
      [userId, sevenDaysAgo.toISOString()],
    );

    // Calculate streak (consecutive days with study sessions) - timezone based
    const streakResult = await pool.query(
      `SELECT created_at
       FROM study_sessions
       WHERE user_id = $1
       ORDER BY created_at DESC`,
      [userId],
    );

    let currentStreak = 0;
    if (streakResult.rows.length > 0) {
      // Get unique study dates in user's timezone YYYY-MM-DD format
      const studyDates = [
        ...new Set(
          streakResult.rows.map((row) => {
            const date = new Date(row.created_at);
            // Use user's timezone for consistent timezone handling
            return formatDateInTimezone(date, userTimezone); // YYYY-MM-DD in user's timezone
          }),
        ),
      ]
        .sort()
        .reverse(); // Sort dates in descending order

      // Get current date in user's timezone
      const now = new Date();
      const todayFormatted = formatDateInTimezone(now, userTimezone);

      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);

      // Check if user studied today or yesterday in user's timezone to start the streak
      const currentDate = studyDates.includes(todayFormatted) ? now : yesterday;
      let currentDateFormatted = formatDateInTimezone(currentDate, userTimezone);

      // Count consecutive days backwards in user's timezone
      while (studyDates.includes(currentDateFormatted)) {
        currentStreak++;
        currentDate.setDate(currentDate.getDate() - 1);
        currentDateFormatted = formatDateInTimezone(currentDate, userTimezone);
      }
    }

    // Get achievements
    const achievementsResult = await pool.query(
      'SELECT * FROM achievements WHERE user_id = $1 ORDER BY unlocked_at DESC',
      [userId],
    );

    // Aggregate session statistics
    const totalSessions = sessionsResult.rows.length;
    const totalCardsStudied = sessionsResult.rows.reduce(
      (sum, session) => sum + session.cards_studied,
      0,
    );
    const totalStudyTime = sessionsResult.rows.reduce(
      (sum, session) => sum + session.duration_seconds,
      0,
    );

    // Calculate weekly progress (last 7 days) - timezone based
    // Reuse the existing sevenDaysAgo variable

    const weeklySessions = await pool.query(
      'SELECT COUNT(*) as session_count, SUM(cards_studied) as cards_count FROM study_sessions WHERE user_id = $1 AND created_at >= $2',
      [userId, sevenDaysAgo.toISOString()],
    );

    const weeklyStats = {
      sessions: parseInt(weeklySessions.rows[0].session_count) || 0,
      cardsStudied: parseInt(weeklySessions.rows[0].cards_count) || 0,
    };

    // Calculate today's progress (cards studied today) - timezone based
    const now = new Date();
    const todayStart = getStartOfDayInTimezone(now, userTimezone);
    const todayEnd = getEndOfDayInTimezone(now, userTimezone);

    const todaySessions = await pool.query(
      'SELECT SUM(cards_studied) as cards_count FROM study_sessions WHERE user_id = $1 AND created_at >= $2 AND created_at <= $3',
      [userId, todayStart.toISOString(), todayEnd.toISOString()],
    );

    const todayProgress = {
      cardsStudied: parseInt(todaySessions.rows[0].cards_count) || 0,
    };

    // Update progress record with current streak
    if (progress) {
      await pool.query(
        `UPDATE user_progress
         SET current_streak_days = $1, updated_at = NOW()
         WHERE user_id = $2`,
        [currentStreak, userId],
      );
    }

    const analytics = {
      progress: {
        totalCardsStudied: progress?.total_cards_studied || 0,
        totalSessions: progress?.total_sessions || 0,
        currentStreakDays: currentStreak,
        longestStreakDays: progress?.longest_streak_days || 0,
        lastStudyDate: progress?.last_study_date,
      },
      recentActivity: {
        sessions: sessionsResult.rows.map((session) => ({
          ...session,
          // Convert created_at to user's timezone for display
          date: new Date(session.created_at),
        })),
        totalCardsStudied,
        totalStudyTime,
        averageSessionTime: totalSessions > 0 ? Math.round(totalStudyTime / totalSessions) : 0,
      },
      weeklyStats,
      todayProgress,
      achievements: achievementsResult.rows,
    };

    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Error fetching progress analytics:', error);
    return NextResponse.json({ error: 'Failed to fetch progress analytics' }, { status: 500 });
  }
}
